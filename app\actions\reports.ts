"use server"

import { sql } from "@/lib/db"

export async function getInventoryValueOverTime(months = 6) {
  try {
    const result = await sql`
      WITH months AS (
        SELECT generate_series(
          date_trunc('month', CURRENT_DATE) - (${months - 1} || ' months')::interval,
          date_trunc('month', CURRENT_DATE),
          '1 month'::interval
        ) as month
      ),
      monthly_movements AS (
        SELECT 
          date_trunc('month', sm.created_at) as month,
          sm.product_id,
          sm.movement_type,
          sm.quantity,
          p.price
        FROM stock_movements sm
        JOIN products p ON sm.product_id = p.id
        WHERE sm.created_at >= date_trunc('month', CURRENT_DATE) - (${months - 1} || ' months')::interval
      ),
      monthly_values AS (
        SELECT
          month,
          SUM(CASE WHEN movement_type = 'in' THEN quantity * price ELSE 0 END) as value_in,
          SUM(CASE WHEN movement_type = 'out' THEN quantity * price ELSE 0 END) as value_out
        FROM monthly_movements
        GROUP BY month
      )
      SELECT 
        to_char(m.month, 'Mon') as month,
        COALESCE(mv.value_in, 0) as value_in,
        COALESCE(mv.value_out, 0) as value_out,
        COALESCE(mv.value_in, 0) - COALESCE(mv.value_out, 0) as net_value
      FROM months m
      LEFT JOIN monthly_values mv ON m.month = mv.month
      ORDER BY m.month
    `

    return result.map((row) => ({
      month: row.month,
      value: Number.parseFloat(row.net_value || "0"),
    }))
  } catch (error) {
    console.error("Erro ao buscar valor do inventário ao longo do tempo:", error)
    return []
  }
}

export async function getCategoryDistribution() {
  try {
    const result = await sql`
      SELECT 
        c.name,
        COUNT(p.id) as product_count,
        SUM(p.current_quantity) as total_quantity,
        SUM(p.current_quantity * p.price) as total_value
      FROM categories c
      JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY total_value DESC
    `

    if (!result || result.length === 0) {
      return []
    }

    const total = result.reduce((sum, row) => sum + Number.parseFloat(row.total_value || "0"), 0)

    if (total === 0) {
      return []
    }

    return result.map((row) => ({
      name: row.name,
      value: Math.round((Number.parseFloat(row.total_value || "0") / total) * 100),
    }))
  } catch (error) {
    console.error("Erro ao buscar distribuição por categoria:", error)
    return []
  }
}

export async function getTopProducts(limit = 5) {
  try {
    const result = await sql`
      SELECT 
        p.id,
        p.name,
        COUNT(sm.id) as movements,
        SUM(p.price * sm.quantity) as value
      FROM products p
      JOIN stock_movements sm ON p.id = sm.product_id
      GROUP BY p.id, p.name
      ORDER BY movements DESC
      LIMIT ${limit}
    `

    return result.map((row) => ({
      id: row.id,
      name: row.name,
      movements: Number.parseInt(row.movements || "0"),
      value: Number.parseFloat(row.value || "0"),
    }))
  } catch (error) {
    console.error("Erro ao buscar produtos principais:", error)
    return []
  }
}

export async function getStockLevelsByCategory() {
  try {
    const result = await sql`
      SELECT 
        c.name,
        SUM(p.current_quantity) as total
      FROM categories c
      JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY total DESC
    `

    return result.map((row) => ({
      name: row.name,
      total: Number.parseInt(row.total || "0"),
    }))
  } catch (error) {
    console.error("Erro ao buscar níveis de estoque por categoria:", error)
    return []
  }
}

