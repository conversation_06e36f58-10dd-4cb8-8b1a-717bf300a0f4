"use server"

import { sql } from "@/lib/db"
import { revalidatePath } from "next/cache"

export type StockMovement = {
  id: number
  product_id: number
  product_name?: string
  movement_type: "in" | "out"
  quantity: number
  reference: string | null
  notes: string | null
  created_by: string | null
  created_at: string
}

export async function getStockMovements(limit = 100): Promise<StockMovement[]> {
  try {
    const result = await sql`
      SELECT sm.*, p.name as product_name
      FROM stock_movements sm
      JOIN products p ON sm.product_id = p.id
      ORDER BY sm.created_at DESC
      LIMIT ${limit}
    `

    return result.map((row) => ({
      ...row,
      created_at: new Date(row.created_at).toISOString(),
    })) as StockMovement[]
  } catch (error) {
    console.error("Erro ao buscar movimentações de estoque:", error)
    return []
  }
}

export async function getRecentStockMovements(limit = 5): Promise<StockMovement[]> {
  try {
    const result = await sql`
      SELECT sm.*, p.name as product_name
      FROM stock_movements sm
      JOIN products p ON sm.product_id = p.id
      ORDER BY sm.created_at DESC
      LIMIT ${limit}
    `

    return result.map((row) => ({
      ...row,
      created_at: new Date(row.created_at).toISOString(),
    })) as StockMovement[]
  } catch (error) {
    console.error("Erro ao buscar movimentações recentes:", error)
    return []
  }
}

export async function createStockMovement(data: {
  product_id: number
  movement_type: "in" | "out"
  quantity: number
  reference?: string
  notes?: string
  created_by?: string
}) {
  try {
    // Iniciar uma transação
    await sql`BEGIN`

    // Verificar se o produto existe
    const productResult = await sql`
      SELECT id, current_quantity FROM products WHERE id = ${data.product_id}
    `

    if (productResult.length === 0) {
      await sql`ROLLBACK`
      return { success: false, error: "Produto não encontrado" }
    }

    const product = productResult[0]
    let newQuantity = product.current_quantity

    // Atualizar a quantidade do produto
    if (data.movement_type === "in") {
      newQuantity += data.quantity
    } else {
      // Verificar se há estoque suficiente para saída
      if (product.current_quantity < data.quantity) {
        await sql`ROLLBACK`
        return { success: false, error: "Quantidade insuficiente em estoque" }
      }
      newQuantity -= data.quantity
    }

    // Registrar a movimentação
    const movementResult = await sql`
      INSERT INTO stock_movements (
        product_id, movement_type, quantity, reference, notes, created_by
      )
      VALUES (
        ${data.product_id},
        ${data.movement_type},
        ${data.quantity},
        ${data.reference || null},
        ${data.notes || null},
        ${data.created_by || null}
      )
      RETURNING id
    `

    // Atualizar a quantidade do produto
    await sql`
      UPDATE products
      SET current_quantity = ${newQuantity}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ${data.product_id}
    `

    // Confirmar a transação
    await sql`COMMIT`

    revalidatePath("/movements")
    revalidatePath("/inventory")
    revalidatePath("/dashboard")

    return { success: true, id: movementResult[0].id }
  } catch (error) {
    await sql`ROLLBACK`
    console.error("Erro ao criar movimentação de estoque:", error)
    return { success: false, error: "Falha ao registrar movimentação" }
  }
}

export async function getPendingOrdersCount() {
  try {
    // Esta é uma consulta de exemplo - você pode precisar ajustá-la com base em como
    // você define "pedidos pendentes" no seu sistema
    const result = await sql`
      SELECT COUNT(*) as count
      FROM stock_movements 
      WHERE movement_type = 'out' AND reference LIKE 'PED-%' AND created_at > CURRENT_DATE - INTERVAL '7 days'
    `
    if (!result || result.length === 0) {
      return 0
    }
    return Number.parseInt(result[0].count)
  } catch (error) {
    console.error("Erro ao contar pedidos pendentes:", error)
    return 0
  }
}

export async function getStockMovementsByProduct(productId: number): Promise<StockMovement[]> {
  try {
    const result = await sql`
      SELECT sm.*, p.name as product_name
      FROM stock_movements sm
      JOIN products p ON sm.product_id = p.id
      WHERE sm.product_id = ${productId}
      ORDER BY sm.created_at DESC
    `

    return result.map((row) => ({
      ...row,
      created_at: new Date(row.created_at).toISOString(),
    })) as StockMovement[]
  } catch (error) {
    console.error(`Erro ao buscar movimentações do produto ${productId}:`, error)
    return []
  }
}

export async function getStockMovementsByType(type: "in" | "out", limit = 100): Promise<StockMovement[]> {
  try {
    const result = await sql`
      SELECT sm.*, p.name as product_name
      FROM stock_movements sm
      JOIN products p ON sm.product_id = p.id
      WHERE sm.movement_type = ${type}
      ORDER BY sm.created_at DESC
      LIMIT ${limit}
    `

    return result.map((row) => ({
      ...row,
      created_at: new Date(row.created_at).toISOString(),
    })) as StockMovement[]
  } catch (error) {
    console.error(`Erro ao buscar movimentações do tipo ${type}:`, error)
    return []
  }
}

export async function getStockMovementStats(months = 6) {
  try {
    const result = await sql`
      WITH months AS (
        SELECT generate_series(
          date_trunc('month', CURRENT_DATE) - (${months - 1} || ' months')::interval,
          date_trunc('month', CURRENT_DATE),
          '1 month'::interval
        ) as month
      ),
      in_movements AS (
        SELECT 
          date_trunc('month', created_at) as month,
          SUM(quantity) as total
        FROM stock_movements
        WHERE movement_type = 'in'
        GROUP BY date_trunc('month', created_at)
      ),
      out_movements AS (
        SELECT 
          date_trunc('month', created_at) as month,
          SUM(quantity) as total
        FROM stock_movements
        WHERE movement_type = 'out'
        GROUP BY date_trunc('month', created_at)
      )
      SELECT 
        to_char(m.month, 'Mon') as month,
        COALESCE(i.total, 0) as in_total,
        COALESCE(o.total, 0) as out_total
      FROM months m
      LEFT JOIN in_movements i ON m.month = i.month
      LEFT JOIN out_movements o ON m.month = o.month
      ORDER BY m.month
    `

    return result.map((row) => ({
      month: row.month,
      in: Number.parseInt(row.in_total || "0"),
      out: Number.parseInt(row.out_total || "0"),
    }))
  } catch (error) {
    console.error("Erro ao buscar estatísticas de movimentação:", error)
    return []
  }
}

