import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Overview } from "@/components/dashboard/overview"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { StockLevels } from "@/components/dashboard/stock-levels"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { getTotalProductCount, getLowStockCount, getTotalStockValue } from "@/app/actions/products"
import { getPendingOrdersCount } from "@/app/actions/stock-movements"
import { formatCurrency } from "@/lib/utils"

export default async function DashboardPage() {
  // Buscar dados reais do banco de dados
  const totalProducts = await getTotalProductCount()
  const lowStockItems = await getLowStockCount()
  const totalStockValue = await getTotalStockValue()
  const pendingOrders = await getPendingOrdersCount()

  return (
    <div className="flex min-h-screen w-full flex-col">
      <div className="flex flex-col">
        <DashboardHeader />
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border-l-4 border-l-category1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-category1"
                >
                  <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalProducts}</div>
                <p className="text-xs text-muted-foreground">Produtos cadastrados no sistema</p>
              </CardContent>
            </Card>
            <Card className="border-l-4 border-l-warning">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Itens com Estoque Baixo</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-warning"
                >
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{lowStockItems}</div>
                <p className="text-xs text-muted-foreground">Produtos que precisam de reposição</p>
              </CardContent>
            </Card>
            <Card className="border-l-4 border-l-category2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Valor do Estoque</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-category2"
                >
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <path d="M2 10h20" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(totalStockValue)}</div>
                <p className="text-xs text-muted-foreground">Valor total do inventário atual</p>
              </CardContent>
            </Card>
            <Card className="border-l-4 border-l-category3">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pedidos Pendentes</CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-category3"
                >
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{pendingOrders}</div>
                <p className="text-xs text-muted-foreground">Pedidos aguardando processamento</p>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Tabs defaultValue="overview" className="col-span-4">
              <TabsList className="gradient-bg">
                <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                <TabsTrigger value="analytics">Análises</TabsTrigger>
              </TabsList>
              <TabsContent value="overview" className="space-y-4">
                <Overview />
              </TabsContent>
              <TabsContent value="analytics" className="space-y-4">
                <Overview />
              </TabsContent>
            </Tabs>
            <Card className="col-span-3 border-t-4 border-t-category4">
              <CardHeader>
                <CardTitle>Atividade Recente</CardTitle>
                <CardDescription>Movimentações e atualizações recentes de estoque</CardDescription>
              </CardHeader>
              <CardContent>
                <RecentActivity />
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4">
            <Card className="col-span-4 border-t-4 border-t-category1">
              <CardHeader>
                <CardTitle>Níveis de Estoque</CardTitle>
                <CardDescription>Níveis atuais de inventário por categoria</CardDescription>
              </CardHeader>
              <CardContent>
                <StockLevels />
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}

