"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface AddMovementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// Mock product data for the select dropdown
const productOptions = [
  { id: "1", name: "Cadeira de Escritório" },
  { id: "2", name: "Luminária de Mesa" },
  { id: "3", name: "Teclado Sem Fio" },
  { id: "4", name: "<PERSON>porte para Monitor" },
  { id: "5", name: "<PERSON>" },
  { id: "6", name: "Mouse Sem Fio" },
  { id: "7", name: "Fones de Ouvido" },
  { id: "8", name: "Armário de Arquivos" },
]

export function AddMovementDialog({ open, onOpenChange }: AddMovementDialogProps) {
  const [formData, setFormData] = useState({
    productId: "",
    type: "in",
    quantity: "",
    reference: "",
    notes: "",
  })

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically save the movement to your database
    console.log("Movement data:", formData)
    // Reset form and close dialog
    setFormData({
      productId: "",
      type: "in",
      quantity: "",
      reference: "",
      notes: "",
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Registrar Movimentação de Estoque</DialogTitle>
            <DialogDescription>Registre entradas ou saídas de estoque para um produto.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="product">Produto</Label>
              <Select value={formData.productId} onValueChange={(value) => handleChange("productId", value)}>
                <SelectTrigger id="product">
                  <SelectValue placeholder="Selecione o produto" />
                </SelectTrigger>
                <SelectContent>
                  {productOptions.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label>Tipo de Movimentação</Label>
              <RadioGroup
                value={formData.type}
                onValueChange={(value) => handleChange("type", value)}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="in" id="in" />
                  <Label htmlFor="in">Entrada</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="out" id="out" />
                  <Label htmlFor="out">Saída</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="quantity">Quantidade</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={formData.quantity}
                onChange={(e) => handleChange("quantity", e.target.value)}
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="reference">Referência</Label>
              <Input
                id="reference"
                placeholder="Número de PO, ID do Pedido, etc."
                value={formData.reference}
                onChange={(e) => handleChange("reference", e.target.value)}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="notes">Observações</Label>
              <Textarea
                id="notes"
                placeholder="Informações adicionais sobre esta movimentação"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                rows={2}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit" className="gradient-bg">
              Registrar Movimentação
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

