import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data for top products
const topProductsData = [
  { id: 1, name: "<PERSON><PERSON> de Escritório", movements: 120, value: 23999.88 },
  { id: 2, name: "Teclado Sem Fio", movements: 95, value: 7599.05 },
  { id: 3, name: "<PERSON>", movements: 82, value: 28699.18 },
  { id: 4, name: "Mouse Sem Fio", movements: 78, value: 3119.22 },
  { id: 5, name: "<PERSON><PERSON> de Ouvido", movements: 65, value: 8449.35 },
]

export function TopProductsTable() {
  return (
    <Table>
      <TableHeader className="bg-muted/50">
        <TableRow>
          <TableHead>Produto</TableHead>
          <TableHead className="text-right">Movimentações</TableHead>
          <TableHead className="text-right">Valor</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {topProductsData.map((product) => (
          <TableRow key={product.id}>
            <TableCell className="font-medium">{product.name}</TableCell>
            <TableCell className="text-right">{product.movements}</TableCell>
            <TableCell className="text-right">
              R$ {product.value.toLocaleString("pt-BR", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

