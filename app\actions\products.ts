"use server"

import { sql } from "@/lib/db"
import { revalidatePath } from "next/cache"

export type Product = {
  id: number
  sku: string
  name: string
  description: string | null
  category_id: number | null
  category_name?: string
  price: number
  cost_price: number | null
  current_quantity: number
  min_quantity: number
  image_url: string | null
}

export type ProductWithStatus = Product & {
  status: "Em Estoque" | "Estoque Baixo" | "Sem Estoque"
}

export async function getProducts(): Promise<ProductWithStatus[]> {
  try {
    const result = await sql`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.name ASC
    `

    return result.map((row) => {
      const product = row as Product & { category_name: string | null }
      let status: "Em Estoque" | "Estoque Baixo" | "Sem Estoque"

      if (product.current_quantity <= 0) {
        status = "Sem Estoque"
      } else if (product.current_quantity <= product.min_quantity) {
        status = "Estoque Baixo"
      } else {
        status = "Em Estoque"
      }

      return { ...product, status }
    })
  } catch (error) {
    console.error("Erro ao buscar produtos:", error)
    return []
  }
}

export async function getProductById(id: number): Promise<ProductWithStatus | null> {
  try {
    const result = await sql`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ${id}
    `

    if (result.length === 0) return null

    const product = result[0] as Product & { category_name: string | null }
    let status: "Em Estoque" | "Estoque Baixo" | "Sem Estoque"

    if (product.current_quantity <= 0) {
      status = "Sem Estoque"
    } else if (product.current_quantity <= product.min_quantity) {
      status = "Estoque Baixo"
    } else {
      status = "Em Estoque"
    }

    return { ...product, status }
  } catch (error) {
    console.error(`Erro ao buscar produto ${id}:`, error)
    return null
  }
}

export async function getLowStockProducts(): Promise<ProductWithStatus[]> {
  try {
    const result = await sql`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.current_quantity <= p.min_quantity AND p.current_quantity > 0
      ORDER BY p.name ASC
    `

    return result.map((row) => {
      const product = row as Product & { category_name: string | null }
      return { ...product, status: "Estoque Baixo" as const }
    })
  } catch (error) {
    console.error("Erro ao buscar produtos com estoque baixo:", error)
    return []
  }
}

export async function getOutOfStockProducts(): Promise<ProductWithStatus[]> {
  try {
    const result = await sql`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.current_quantity <= 0
      ORDER BY p.name ASC
    `

    return result.map((row) => {
      const product = row as Product & { category_name: string | null }
      return { ...product, status: "Sem Estoque" as const }
    })
  } catch (error) {
    console.error("Erro ao buscar produtos sem estoque:", error)
    return []
  }
}

export async function createProduct(data: {
  sku: string
  name: string
  description?: string
  category_id?: number
  price: number
  cost_price?: number
  current_quantity: number
  min_quantity?: number
  image_url?: string
}) {
  try {
    // Verificar se o SKU já existe
    const existingSku = await sql`SELECT id FROM products WHERE sku = ${data.sku}`
    if (existingSku.length > 0) {
      return { success: false, error: "SKU já existe" }
    }

    const result = await sql`
      INSERT INTO products (
        sku, name, description, category_id, price, 
        cost_price, current_quantity, min_quantity, image_url
      )
      VALUES (
        ${data.sku},
        ${data.name},
        ${data.description || null},
        ${data.category_id || null},
        ${data.price},
        ${data.cost_price || null},
        ${data.current_quantity},
        ${data.min_quantity || 0},
        ${data.image_url || null}
      )
      RETURNING id
    `

    revalidatePath("/inventory")
    return { success: true, id: result[0].id }
  } catch (error) {
    console.error("Erro ao criar produto:", error)
    return { success: false, error: "Falha ao criar produto" }
  }
}

export async function updateProduct(
  id: number,
  data: {
    sku?: string
    name?: string
    description?: string
    category_id?: number
    price?: number
    cost_price?: number
    min_quantity?: number
    image_url?: string
  },
) {
  try {
    // Verificar se o SKU já existe em outro produto
    if (data.sku) {
      const existingSku = await sql`
        SELECT id FROM products WHERE sku = ${data.sku} AND id != ${id}
      `
      if (existingSku.length > 0) {
        return { success: false, error: "SKU já existe em outro produto" }
      }
    }

    // Construir a consulta SQL dinamicamente com base nos campos fornecidos
    const updateFields = []
    const params = []

    if (data.sku !== undefined) {
      updateFields.push(`sku = $${params.length + 1}`)
      params.push(data.sku)
    }

    if (data.name !== undefined) {
      updateFields.push(`name = $${params.length + 1}`)
      params.push(data.name)
    }

    if (data.description !== undefined) {
      updateFields.push(`description = $${params.length + 1}`)
      params.push(data.description)
    }

    if (data.category_id !== undefined) {
      updateFields.push(`category_id = $${params.length + 1}`)
      params.push(data.category_id)
    }

    if (data.price !== undefined) {
      updateFields.push(`price = $${params.length + 1}`)
      params.push(data.price)
    }

    if (data.cost_price !== undefined) {
      updateFields.push(`cost_price = $${params.length + 1}`)
      params.push(data.cost_price)
    }

    if (data.min_quantity !== undefined) {
      updateFields.push(`min_quantity = $${params.length + 1}`)
      params.push(data.min_quantity)
    }

    if (data.image_url !== undefined) {
      updateFields.push(`image_url = $${params.length + 1}`)
      params.push(data.image_url)
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)

    if (updateFields.length === 0) {
      return { success: false, error: "Nenhum campo para atualizar" }
    }

    const updateQuery = `
      UPDATE products
      SET ${updateFields.join(", ")}
      WHERE id = $${params.length + 1}
      RETURNING id
    `

    params.push(id)

    const result = await sql.query(updateQuery, params)

    revalidatePath("/inventory")
    return { success: true, id: result[0].id }
  } catch (error) {
    console.error(`Erro ao atualizar produto ${id}:`, error)
    return { success: false, error: "Falha ao atualizar produto" }
  }
}

export async function deleteProduct(id: number) {
  try {
    // Verificar se existem movimentações para este produto
    const movementsWithProduct = await sql`
      SELECT COUNT(*) as count FROM stock_movements WHERE product_id = ${id}
    `

    if (Number.parseInt(movementsWithProduct[0].count) > 0) {
      return {
        success: false,
        error: "Não é possível excluir este produto porque existem movimentações associadas a ele",
      }
    }

    await sql`DELETE FROM products WHERE id = ${id}`
    revalidatePath("/inventory")
    return { success: true }
  } catch (error) {
    console.error(`Erro ao excluir produto ${id}:`, error)
    return { success: false, error: "Falha ao excluir produto" }
  }
}

export async function getTotalProductCount() {
  try {
    const result = await sql`SELECT COUNT(*) as count FROM products`
    // Verificar se o resultado existe e tem a propriedade count
    if (!result || result.length === 0) {
      console.error("Resultado da consulta de contagem de produtos está vazio")
      return 0
    }
    return Number.parseInt(result[0].count)
  } catch (error) {
    console.error("Erro ao contar produtos:", error)
    return 0
  }
}

export async function getLowStockCount() {
  try {
    const result = await sql`
      SELECT COUNT(*) as count
      FROM products 
      WHERE current_quantity <= min_quantity AND current_quantity > 0
    `
    if (!result || result.length === 0) {
      return 0
    }
    return Number.parseInt(result[0].count)
  } catch (error) {
    console.error("Erro ao contar produtos com estoque baixo:", error)
    return 0
  }
}

export async function getTotalStockValue() {
  try {
    const result = await sql`
      SELECT SUM(price * current_quantity) as total_value
      FROM products
    `
    if (!result || result.length === 0 || result[0].total_value === null) {
      return 0
    }
    return Number.parseFloat(result[0].total_value) || 0
  } catch (error) {
    console.error("Erro ao calcular valor total do estoque:", error)
    return 0
  }
}

