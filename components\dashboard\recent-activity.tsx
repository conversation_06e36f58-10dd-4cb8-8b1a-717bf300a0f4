import { ArrowDown, ArrowU<PERSON> } from "lucide-react"
import { getRecentStockMovements } from "@/app/actions/stock-movements"
import { formatRelativeTime } from "@/lib/utils"

export async function RecentActivity() {
  const recentMovements = await getRecentStockMovements(5)

  if (recentMovements.length === 0) {
    return (
      <div className="flex items-center justify-center h-40">
        <p className="text-muted-foreground">Nenhuma atividade recente encontrada</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {recentMovements.map((item) => (
        <div key={item.id} className="flex items-center">
          <div className={`mr-4 rounded-full p-2 ${item.movement_type === "in" ? "bg-green-100" : "bg-pink-100"}`}>
            {item.movement_type === "in" ? (
              <ArrowDown className="h-4 w-4 text-green-600" />
            ) : (
              <ArrowUp className="h-4 w-4 text-pink-600" />
            )}
          </div>
          <div className="flex-1 space-y-1">
            <p className="text-sm font-medium leading-none">
              {item.movement_type === "in" ? "Entrada" : "Saída"}: {item.product_name}
            </p>
            <p className="text-sm text-muted-foreground">Quantidade: {item.quantity}</p>
          </div>
          <div className="text-sm text-muted-foreground">{formatRelativeTime(new Date(item.created_at))}</div>
        </div>
      ))}
    </div>
  )
}

