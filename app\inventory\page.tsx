import { Suspense } from "react"
import { InventoryContent } from "@/components/inventory/inventory-content"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { Skeleton } from "@/components/ui/skeleton"

export default function InventoryPage() {
  return (
    <div className="flex min-h-screen w-full flex-col">
      <DashboardHeader />
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Gestão de Inventário</h1>
        </div>

        <Suspense fallback={<InventorySkeleton />}>
          <InventoryContent />
        </Suspense>
      </main>
    </div>
  )
}

function InventorySkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <Skeleton className="h-10 w-[250px]" />
        <Skeleton className="h-10 w-[150px]" />
      </div>
      <Skeleton className="h-[600px] w-full" />
    </div>
  )
}

