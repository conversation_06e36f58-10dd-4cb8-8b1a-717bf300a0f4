"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Bell, Menu, Package, Settings } from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
    active: (pathname: string) => pathname === "/dashboard",
  },
  {
    href: "/inventory",
    label: "Inventário",
    active: (pathname: string) => pathname === "/inventory",
  },
  {
    href: "/movements",
    label: "Movimentações",
    active: (pathname: string) => pathname === "/movements",
  },
  {
    href: "/reports",
    label: "Relatórios",
    active: (pathname: string) => pathname === "/reports",
  },
]

export function DashboardHeader() {
  const pathname = usePathname()

  return (
    <header className="sticky top-0 z-50 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Alternar Menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-72">
          <nav className="grid gap-6 text-lg font-medium">
            <Link href="/" className="flex items-center gap-2 text-lg font-semibold">
              <Package className="h-6 w-6 text-category1" />
              <span className="gradient-text">EstoqueManager</span>
            </Link>
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                className={cn(
                  "flex items-center gap-2",
                  route.active(pathname) ? "text-primary" : "text-muted-foreground",
                )}
              >
                {route.label}
              </Link>
            ))}
          </nav>
        </SheetContent>
      </Sheet>
      <div className="flex items-center gap-2">
        <Link href="/" className="flex items-center gap-2 text-lg font-semibold">
          <Package className="h-6 w-6 text-category1" />
          <span className="hidden md:inline-block bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-500">
            EstoqueManager
          </span>
        </Link>
      </div>
      <nav className="hidden flex-1 md:flex">
        <ul className="flex flex-1 items-center gap-4 text-sm font-medium">
          {routes.map((route) => (
            <li key={route.href}>
              <Link
                href={route.href}
                className={cn(
                  "flex items-center gap-1 px-4 py-2 transition-colors hover:text-primary",
                  route.active(pathname) ? "text-primary" : "text-muted-foreground",
                )}
              >
                {route.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon">
          <Bell className="h-5 w-5" />
          <span className="sr-only">Notificações</span>
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <Settings className="h-5 w-5" />
              <span className="sr-only">Configurações</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Minha Conta</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Configurações</DropdownMenuItem>
            <DropdownMenuItem>Suporte</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Sair</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}

