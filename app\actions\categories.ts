"use server"

import { sql } from "@/lib/db"
import { revalidatePath } from "next/cache"

export type Category = {
  id: number
  name: string
  description: string | null
}

export async function getCategories(): Promise<Category[]> {
  try {
    const result = await sql`
      SELECT id, name, description 
      FROM categories 
      ORDER BY name ASC
    `
    return result as Category[]
  } catch (error) {
    console.error("Erro ao buscar categorias:", error)
    return []
  }
}

export async function getCategoryById(id: number): Promise<Category | null> {
  try {
    const result = await sql`
      SELECT id, name, description 
      FROM categories 
      WHERE id = ${id}
    `
    return result.length > 0 ? (result[0] as Category) : null
  } catch (error) {
    console.error(`Erro ao buscar categoria ${id}:`, error)
    return null
  }
}

export async function createCategory(data: { name: string; description?: string }) {
  try {
    const result = await sql`
      INSERT INTO categories (name, description)
      VALUES (${data.name}, ${data.description || null})
      RETURNING id, name, description
    `
    revalidatePath("/inventory")
    return { success: true, data: result[0] }
  } catch (error) {
    console.error("Erro ao criar categoria:", error)
    return { success: false, error: "Falha ao criar categoria" }
  }
}

export async function updateCategory(id: number, data: { name: string; description?: string }) {
  try {
    const result = await sql`
      UPDATE categories
      SET name = ${data.name}, 
          description = ${data.description || null},
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, description
    `
    revalidatePath("/inventory")
    return { success: true, data: result[0] }
  } catch (error) {
    console.error(`Erro ao atualizar categoria ${id}:`, error)
    return { success: false, error: "Falha ao atualizar categoria" }
  }
}

export async function deleteCategory(id: number) {
  try {
    // Verificar se existem produtos usando esta categoria
    const productsWithCategory = await sql`
      SELECT COUNT(*) as count FROM products WHERE category_id = ${id}
    `

    if (Number.parseInt(productsWithCategory[0].count) > 0) {
      return {
        success: false,
        error: "Não é possível excluir esta categoria porque existem produtos associados a ela",
      }
    }

    await sql`DELETE FROM categories WHERE id = ${id}`
    revalidatePath("/inventory")
    return { success: true }
  } catch (error) {
    console.error(`Erro ao excluir categoria ${id}:`, error)
    return { success: false, error: "Falha ao excluir categoria" }
  }
}

