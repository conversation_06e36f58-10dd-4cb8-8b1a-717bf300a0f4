"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { getStockMovementStats } from "@/app/actions/stock-movements"

export function Overview() {
  const [data, setData] = useState<{ month: string; in: number; out: number }[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getStockMovementStats(7)
        setData(result)
      } catch (error) {
        console.error("Erro ao buscar estatísticas de movimentação:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Visão Geral do Estoque</CardTitle>
          <CardDescription>Valor do estoque e tendências de movimentação ao longo do tempo.</CardDescription>
        </CardHeader>
        <CardContent className="px-2">
          <div className="h-[200px] flex items-center justify-center">
            <p className="text-muted-foreground">Carregando dados...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Visão Geral do Estoque</CardTitle>
        <CardDescription>Tendências de movimentação de estoque ao longo do tempo.</CardDescription>
      </CardHeader>
      <CardContent className="px-2">
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 10,
                left: 10,
                bottom: 0,
              }}
            >
              <XAxis dataKey="month" tickLine={false} axisLine={false} tickMargin={10} />
              <YAxis tickLine={false} axisLine={false} tickMargin={10} />
              <Tooltip formatter={(value) => [`${value} unidades`, "Quantidade"]} />
              <Line
                type="monotone"
                dataKey="in"
                name="Entradas"
                stroke="#8b5cf6"
                strokeWidth={2}
                activeDot={{ r: 6 }}
              />
              <Line type="monotone" dataKey="out" name="Saídas" stroke="#ec4899" strokeWidth={2} activeDot={{ r: 6 }} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

