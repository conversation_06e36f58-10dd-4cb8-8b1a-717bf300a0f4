"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { createProduct } from "@/app/actions/products"
import { toast } from "@/components/ui/use-toast"

interface AddProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  categories: { id: number; name: string }[]
  onProductAdded?: (product: any) => void
}

export function AddProductDialog({ open, onOpenChange, categories, onProductAdded }: AddProductDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    sku: "",
    price: "",
    cost_price: "",
    current_quantity: "",
    min_quantity: "",
    description: "",
  })
  const [loading, setLoading] = useState(false)

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const result = await createProduct({
        name: formData.name,
        sku: formData.sku,
        category_id: formData.category_id ? Number.parseInt(formData.category_id) : undefined,
        price: Number.parseFloat(formData.price),
        cost_price: formData.cost_price ? Number.parseFloat(formData.cost_price) : undefined,
        current_quantity: Number.parseInt(formData.current_quantity),
        min_quantity: formData.min_quantity ? Number.parseInt(formData.min_quantity) : undefined,
        description: formData.description || undefined,
      })

      if (result.success) {
        toast({
          title: "Produto adicionado",
          description: `O produto ${formData.name} foi adicionado com sucesso.`,
        })

        if (onProductAdded) {
          onProductAdded({
            id: result.id,
            name: formData.name,
            sku: formData.sku,
            category_id: formData.category_id ? Number.parseInt(formData.category_id) : null,
            category_name: formData.category_id
              ? categories.find((c) => c.id.toString() === formData.category_id)?.name
              : null,
            price: Number.parseFloat(formData.price),
            cost_price: formData.cost_price ? Number.parseFloat(formData.cost_price) : null,
            current_quantity: Number.parseInt(formData.current_quantity),
            min_quantity: formData.min_quantity ? Number.parseInt(formData.min_quantity) : 0,
            description: formData.description || null,
            image_url: null,
          })
        }

        // Reset form and close dialog
        setFormData({
          name: "",
          category_id: "",
          sku: "",
          price: "",
          cost_price: "",
          current_quantity: "",
          min_quantity: "",
          description: "",
        })
        onOpenChange(false)
      } else {
        toast({
          title: "Erro",
          description: result.error || "Ocorreu um erro ao adicionar o produto.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Erro ao adicionar produto:", error)
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao adicionar o produto.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Adicionar Novo Produto</DialogTitle>
            <DialogDescription>Insira os detalhes do novo produto para adicionar ao seu inventário.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nome do Produto</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Categoria</Label>
                <Select value={formData.category_id} onValueChange={(value) => handleChange("category_id", value)}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Selecione a categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="sku">SKU</Label>
                <Input id="sku" value={formData.sku} onChange={(e) => handleChange("sku", e.target.value)} required />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="price">Preço (R$)</Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => handleChange("price", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="cost_price">Preço de Custo (R$)</Label>
                <Input
                  id="cost_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cost_price}
                  onChange={(e) => handleChange("cost_price", e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="current_quantity">Quantidade Inicial</Label>
                <Input
                  id="current_quantity"
                  type="number"
                  min="0"
                  value={formData.current_quantity}
                  onChange={(e) => handleChange("current_quantity", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="min_quantity">Quantidade Mínima</Label>
              <Input
                id="min_quantity"
                type="number"
                min="0"
                value={formData.min_quantity}
                onChange={(e) => handleChange("min_quantity", e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancelar
            </Button>
            <Button type="submit" className="gradient-bg" disabled={loading}>
              {loading ? "Adicionando..." : "Adicionar Produto"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

