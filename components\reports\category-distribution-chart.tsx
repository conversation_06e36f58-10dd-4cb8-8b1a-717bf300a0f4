"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

// Mock data for category distribution
const categoryData = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: 40 },
  { name: "Eletrônicos", value: 30 },
  { name: "Acessórios", value: 20 },
  { name: "Material de Escritório", value: 10 },
]

const COLORS = ["#8b5cf6", "#ec4899", "#06b6d4", "#f97316"]

export function CategoryDistributionChart() {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={categoryData}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={100}
          paddingAngle={2}
          dataKey="value"
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          labelLine={false}
        >
          {categoryData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip
          formatter={(value: number) => [`${value}%`, "Porcentagem"]}
          labelFormatter={(label) => `Categoria: ${label}`}
        />
      </PieChart>
    </ResponsiveContainer>
  )
}

