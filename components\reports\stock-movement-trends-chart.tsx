"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

// Mock data for stock movement trends
const movementTrendsData = [
  { month: "Jan", in: 120, out: 80 },
  { month: "Fev", in: 150, out: 100 },
  { month: "Mar", in: 180, out: 160 },
  { month: "Abr", in: 170, out: 140 },
  { month: "Mai", in: 200, out: 180 },
  { month: "Jun", in: 220, out: 190 },
]

export function StockMovementTrendsChart() {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={movementTrendsData} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
        <XAxis dataKey="month" tickLine={false} axisLine={false} tickMargin={10} />
        <YAxis tickLine={false} axisLine={false} tickMargin={10} />
        <Tooltip />
        <Legend />
        <Bar dataKey="in" name="<PERSON>tra<PERSON>" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
        <Bar dataKey="out" name="<PERSON><PERSON><PERSON>" fill="#ec4899" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

