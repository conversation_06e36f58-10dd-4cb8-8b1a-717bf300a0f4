"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { useEffect, useState } from "react"
import { getStockLevelsByCategory } from "@/app/actions/reports"

export function StockLevels() {
  const [data, setData] = useState<{ name: string; total: number }[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getStockLevelsByCategory()
        setData(result)
      } catch (error) {
        console.error("Erro ao buscar níveis de estoque:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">Carregando dados...</p>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">Nenhum dado disponível</p>
      </div>
    )
  }

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <XAxis dataKey="name" tickLine={false} axisLine={false} tickMargin={10} />
          <YAxis tickLine={false} axisLine={false} tickMargin={10} />
          <Tooltip
            formatter={(value) => [`${value} unidades`, "Quantidade"]}
            labelFormatter={(label) => `Categoria: ${label}`}
          />
          <Bar dataKey="total" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

