"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

// Mock data for inventory value over time
const inventoryValueData = [
  { month: "Jan", value: 35000 },
  { month: "Fev", value: 38000 },
  { month: "Mar", value: 42000 },
  { month: "Abr", value: 45000 },
  { month: "Mai", value: 43000 },
  { month: "Jun", value: 48000 },
]

export function InventoryValueChart() {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={inventoryValueData} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
        <XAxis dataKey="month" tickLine={false} axisLine={false} tickMargin={10} />
        <YAxis
          tickLine={false}
          axisLine={false}
          tickMargin={10}
          tickFormatter={(value) => `R$ ${value.toLocaleString()}`}
        />
        <Tooltip
          formatter={(value: number) => [`R$ ${value.toLocaleString()}`, "Valor do Inventário"]}
          labelFormatter={(label) => `Mês: ${label}`}
        />
        <Line type="monotone" dataKey="value" stroke="#8b5cf6" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
      </LineChart>
    </ResponsiveContainer>
  )
}

