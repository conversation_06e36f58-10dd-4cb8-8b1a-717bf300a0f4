"use client"

import { useState } from "react"
import { <PERSON>D<PERSON>, ArrowU<PERSON>, Calendar, Download, Search } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { AddMovementDialog } from "@/components/movements/add-movement-dialog"

// Mock data for stock movements
const movementsData = [
  {
    id: "MOV001",
    date: "01/04/2023",
    product: "Cadeira de Escritório",
    type: "in",
    quantity: 10,
    reference: "PO-2023-001",
    notes: "Reabastecimento",
  },
  {
    id: "MOV002",
    date: "02/04/2023",
    product: "Luminária de Mesa",
    type: "out",
    quantity: 2,
    reference: "PED-2023-042",
    notes: "Pedido de cliente",
  },
  {
    id: "MOV003",
    date: "03/04/2023",
    product: "Teclado Sem Fio",
    type: "in",
    quantity: 15,
    reference: "PO-2023-002",
    notes: "Novo inventário",
  },
  {
    id: "MOV004",
    date: "04/04/2023",
    product: "Suporte para Monitor",
    type: "out",
    quantity: 5,
    reference: "PED-2023-043",
    notes: "Pedido em massa",
  },
  {
    id: "MOV005",
    date: "05/04/2023",
    product: "Mesa",
    type: "in",
    quantity: 8,
    reference: "PO-2023-003",
    notes: "Reabastecimento",
  },
  {
    id: "MOV006",
    date: "06/04/2023",
    product: "Mouse Sem Fio",
    type: "out",
    quantity: 3,
    reference: "PED-2023-044",
    notes: "Pedido de cliente",
  },
  {
    id: "MOV007",
    date: "07/04/2023",
    product: "Fones de Ouvido",
    type: "in",
    quantity: 12,
    reference: "PO-2023-004",
    notes: "Novo inventário",
  },
  {
    id: "MOV008",
    date: "08/04/2023",
    product: "Armário de Arquivos",
    type: "out",
    quantity: 2,
    reference: "PED-2023-045",
    notes: "Pedido de cliente",
  },
]

export default function MovementsPage() {
  const [isAddMovementOpen, setIsAddMovementOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [movementType, setMovementType] = useState("all")

  // Filter movements based on search and type
  const filteredMovements = movementsData.filter((item) => {
    const matchesSearch =
      item.product.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.reference.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = movementType === "all" || item.type === movementType

    return matchesSearch && matchesType
  })

  return (
    <div className="flex min-h-screen w-full flex-col">
      <DashboardHeader />
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Movimentações de Estoque</h1>
          <Button onClick={() => setIsAddMovementOpen(true)} className="gradient-bg">
            Registrar Movimentação
          </Button>
        </div>

        <Card className="border-t-4 border-t-category3">
          <CardHeader>
            <CardTitle>Histórico de Movimentações</CardTitle>
            <CardDescription>
              Acompanhe todas as movimentações de estoque, incluindo entradas e saídas de inventário.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Buscar movimentações..."
                      className="pl-8 sm:w-[300px]"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select value={movementType} onValueChange={setMovementType}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Tipo de Movimentação" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as Movimentações</SelectItem>
                      <SelectItem value="in">Entrada</SelectItem>
                      <SelectItem value="out">Saída</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Calendar className="mr-2 h-4 w-4" />
                    Período
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Exportar
                  </Button>
                </div>
              </div>
              <div className="rounded-md border">
                <Table>
                  <TableHeader className="bg-muted/50">
                    <TableRow>
                      <TableHead className="w-[100px]">ID</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Produto</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead className="text-right">Quantidade</TableHead>
                      <TableHead>Referência</TableHead>
                      <TableHead>Observações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMovements.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.id}</TableCell>
                        <TableCell>{item.date}</TableCell>
                        <TableCell>{item.product}</TableCell>
                        <TableCell>
                          <Badge
                            className={item.type === "in" ? "badge-success" : "bg-category2 text-white"}
                            style={{ width: "80px", display: "flex", justifyContent: "center" }}
                          >
                            {item.type === "in" ? (
                              <>
                                <ArrowDown className="mr-1 h-3 w-3" /> Entrada
                              </>
                            ) : (
                              <>
                                <ArrowUp className="mr-1 h-3 w-3" /> Saída
                              </>
                            )}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">{item.quantity}</TableCell>
                        <TableCell>{item.reference}</TableCell>
                        <TableCell>{item.notes}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
      <AddMovementDialog open={isAddMovementOpen} onOpenChange={setIsAddMovementOpen} />
    </div>
  )
}

