"use client"

import { Download, Filter } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { InventoryValueChart } from "@/components/reports/inventory-value-chart"
import { CategoryDistributionChart } from "@/components/reports/category-distribution-chart"
import { StockMovementTrendsChart } from "@/components/reports/stock-movement-trends-chart"
import { TopProductsTable } from "@/components/reports/top-products-table"

export default function ReportsPage() {
  return (
    <div className="flex min-h-screen w-full flex-col">
      <DashboardHeader />
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold"><PERSON>lató<PERSON>s & Análises</h1>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filtrar
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="gradient-bg">
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="inventory">Inventário</TabsTrigger>
            <TabsTrigger value="movements">Movimentações</TabsTrigger>
            <TabsTrigger value="valuation">Avaliação</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
              <Card className="border-l-4 border-l-category1">
                <CardHeader>
                  <CardTitle>Valor do Inventário ao Longo do Tempo</CardTitle>
                  <CardDescription>Valor total do inventário nos últimos 6 meses</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <InventoryValueChart />
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-category2">
                <CardHeader>
                  <CardTitle>Distribuição por Categoria</CardTitle>
                  <CardDescription>Distribuição de produtos por categoria</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <CategoryDistributionChart />
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
              <Card className="border-l-4 border-l-category3">
                <CardHeader>
                  <CardTitle>Tendências de Movimentação</CardTitle>
                  <CardDescription>Entradas vs saídas de estoque ao longo do tempo</CardDescription>
                </CardHeader>
                <CardContent className="h-80">
                  <StockMovementTrendsChart />
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-category4">
                <CardHeader>
                  <CardTitle>Produtos Principais</CardTitle>
                  <CardDescription>Produtos com maior volume de movimentação</CardDescription>
                </CardHeader>
                <CardContent>
                  <TopProductsTable />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="inventory" className="space-y-4">
            <Card className="border-t-4 border-t-category1">
              <CardHeader>
                <CardTitle>Relatórios Detalhados de Inventário</CardTitle>
                <CardDescription>Análise e relatórios abrangentes de inventário</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Esta seção conterá relatórios detalhados de inventário, incluindo análise de envelhecimento, taxas de
                  rotatividade e métricas de saúde do inventário.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="movements" className="space-y-4">
            <Card className="border-t-4 border-t-category2">
              <CardHeader>
                <CardTitle>Análise de Movimentações</CardTitle>
                <CardDescription>Análise detalhada das movimentações de estoque</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Esta seção conterá relatórios detalhados de movimentações, incluindo análise de frequência, padrões
                  sazonais e movimentação por origem/destino.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="valuation" className="space-y-4">
            <Card className="border-t-4 border-t-category3">
              <CardHeader>
                <CardTitle>Avaliação de Inventário</CardTitle>
                <CardDescription>Avaliação financeira do inventário atual</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Esta seção conterá relatórios detalhados de avaliação, incluindo análise PEPS/UEPS, cálculos de
                  depreciação e valor por categoria/localização.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

