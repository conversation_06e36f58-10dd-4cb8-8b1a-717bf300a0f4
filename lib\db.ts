import { neon } from "@neondatabase/serverless"

// Cria uma conexão com o banco de dados Neon
export const sql = neon(process.env.DATABASE_URL!)

// Função auxiliar para executar consultas SQL diretamente
export async function query(sqlQuery: string, params: any[] = []) {
  try {
    return await sql.query(sqlQuery, params)
  } catch (error) {
    console.error("Erro ao executar consulta SQL:", error)
    throw error
  }
}

